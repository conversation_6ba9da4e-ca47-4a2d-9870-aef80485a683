$(() => {
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    function renderListCity() {
        let html = '<option value="toan-quoc">Toàn Quốc</option>'
        for (let key in countrys) {
            html += `
        <option value="${removeUtf8ReplaceAll(key)}">${key}</option>
    `
        }
        $('#city-state').html(html);

        let valDefault = $('#city-state').attr('value');
        if (valDefault) $('#city-state').val(valDefault).change();
    }

    // Handle screen type change
    function handleScreenTypeChange() {
        const screenType = $('#screen').val();
        const productContainer = $('#product-dropdown-container');
        const paramsContainer = $('#params-input-container');

        if (screenType === 'PRODUCT_DETAILS') {
            productContainer.show();
            paramsContainer.hide();
            loadProductDropdown();
        } else {
            productContainer.hide();
            paramsContainer.show();
        }
    }

    // Load products for dropdown
    function loadProductDropdown() {
        console.log('Loading products for dropdown...');

        $.ajax({
            url: '/admin/lay-danh-sach-san-pham-dropdown.html',
            method: 'GET',
            success: function(response) {
                console.log('Products loaded:', response);

                if (!response.error && response.data && response.data.products) {
                    let html = '<option value="">-- Chọn sản phẩm --</option>';

                    response.data.products.forEach(product => {
                        const isSelected = news.params === product._id ? 'selected' : '';
                        html += `<option value="${product._id}" ${isSelected}>${product.name}</option>`;
                    });

                    $('#product-select').html(html);
                } else {
                    console.error('Error loading products:', response.message);
                    $('#product-select').html('<option value="">Lỗi tải danh sách sản phẩm</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error loading products:', error);
                $('#product-select').html('<option value="">Lỗi kết nối</option>');
            }
        });
    }

    // Handle product selection
    $('#product-select').on('change', function() {
        const selectedProductId = $(this).val();
        $('#params').val(selectedProductId);
        console.log('Product selected:', selectedProductId);
    });

    // Handle screen type change
    $('#screen').on('change', handleScreenTypeChange);

    // Initialize
    renderListCity();

    // Set initial state based on current screen type
    handleScreenTypeChange();

    $('#form-edit-banner-app').on('submit', (e) => {
        e.preventDefault();

        // Handle product selection for PRODUCT_DETAILS screen type
        const screenType = $('#screen').val();
        if (screenType === 'PRODUCT_DETAILS') {
            const selectedProductId = $('#product-select').val();
            if (selectedProductId) {
                $('#params').val(selectedProductId);
            }
        }

        let data = $('#form-edit-banner-app').serializeArray();
        var formData = new FormData();
        data.forEach(item => {
            formData.append(item.name, item.value);
        })
        formData.append(`thumbail`, $(`#upload-news-0`).prop('files')[0])
        ajaxFile(`/admin/chinh-sua-banner-store/${news._id}.html`, formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/admin/quan-ly-banner-store.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
