<div class="container">
  <main class="cho-hai-san-inner">
    <div class="frame-parent">
      <div class="frame-group align-items-center">
        <!-- Bootstrap Carousel Slider - Full Width -->
        <div id="choHaiSanCarousel" class="carousel slide full-width-carousel" data-bs-ride="carousel"
             (mouseenter)="pauseAutoSlide()" (mouseleave)="resumeAutoSlide()">
          <!-- Loading handled by global loading service -->

          <!-- Error State -->
          <div *ngIf="!isLoadingBanners && bannerError" class="alert alert-danger m-3">
            {{ bannerError }}
          </div>

          <!-- Carousel Content when data loaded -->
          <ng-container *ngIf="!isLoadingBanners && !bannerError && banners.length > 0">
            <!-- Carousel indicators -->
            <div class="carousel-indicators">
              <button
                *ngFor="let banner of banners; let i = index"
                type="button"
                data-bs-target="#choHaiSanCarousel"
                [attr.data-bs-slide-to]="i"
                [class.active]="i === 0"
                [attr.aria-current]="i === 0 ? 'true' : null"
                [attr.aria-label]="'Slide ' + (i + 1)"
              ></button>
            </div>

            <!-- Carousel items -->
            <div class="carousel-inner">
              <div
                *ngFor="let banner of banners; let i = index"
                class="carousel-item"
                [class.active]="i === 0"
                (click)="onImageClick(banner)"
              >
                <img
                  [src]="getBannerImageUrl(banner)"
                  class="d-block w-100 carousel-image image-icon"
                  [alt]="banner.name"
                  style="cursor: pointer"
                />
                <div class="carousel-caption d-none d-md-block">
                  <!-- <h5>{{ banner.name }}</h5> -->
                </div>
              </div>
            </div>

            <!-- Navigation controls -->
            <button
              class="carousel-control-prev"
              type="button"
              data-bs-target="#choHaiSanCarousel"
              data-bs-slide="prev"
            >
              <span class="carousel-control-prev-icon" aria-hidden="true"></span>
              <span class="visually-hidden">Previous</span>
            </button>
            <button
              class="carousel-control-next"
              type="button"
              data-bs-target="#choHaiSanCarousel"
              data-bs-slide="next"
            >
              <span class="carousel-control-next-icon" aria-hidden="true"></span>
              <span class="visually-hidden">Next</span>
            </button>
          </ng-container>

          <!-- Fallback when no banners -->
          <div *ngIf="!isLoadingBanners && !bannerError && banners.length === 0" class="no-banners">
            <img class="image-icon" alt="" src="assets/<EMAIL>" (click)="onImageClick()" />
          </div>
        </div>
        <div class="frame-container">
          <div class="frame-div w-100">
            <div class="ch-hi-sn-lng-nui-bin-v-wrapper">
              <h3 class="ch-hi-sn">CHỢ HẢI SẢN - LÀNG NUÔI BIỂN VÂN ĐỒN</h3>
            </div>
            <div class="frame-parent1 justify-content-center">
              <!-- Tất cả danh mục -->
              <button
                class="rectangle-group align-items-center justify-content-center category-btn"
                [class.active]="selectedCategoryId === ''"
                (click)="onCategorySelect('')">
                <div class="rectangle-div"></div>
                <div class="thc-phm-ti">TẤT CẢ</div>
              </button>

              <!-- Danh mục từ API -->
              <button
                *ngFor="let category of categories"
                class="rectangle-group align-items-center justify-content-center category-btn"
                [class.active]="selectedCategoryId === category._id"
                (click)="onCategorySelect(category._id)">
                <div class="rectangle-div"></div>
                <div class="thc-phm-ti">{{category.name}}</div>
              </button>
            </div>
          </div>
        </div>
        <!-- Products grid -->
        <div class="frame-parent2">
          <app-product-item
            *ngFor="let product of products; trackBy: trackByProductId"
            [image]="product.image"
            [price]="product.price"
            [name]="product.name"
            [priceDiscount]="product.priceDiscount"
            [discount]="product.discount"
            (click)="onProductClick(product)"
            (onBuyClick)="onOrderClick()"
            style="cursor: pointer;">
          </app-product-item>
        </div>

        <!-- Empty state -->
        <div *ngIf="!loading && products.length === 0" class="empty-state text-center p-4">
          <i class="fas fa-box-open fa-3x mb-3 text-muted"></i>
          <h4>Không tìm thấy sản phẩm</h4>
          <p class="text-muted">Thử chọn danh mục khác hoặc thử lại sau</p>
        </div>
      </div>
      <!-- Load More Button -->
      <button
        *ngIf="hasMore && products.length > 0"
        class="frame-wrapper1"
        (click)="onLoadMore()"
        [disabled]="loading">
        <div class="rectangle-parent2">
          <div class="frame-child3"></div>
          <div class="thc-phm-ti">
            <span *ngIf="!loading">XEM THÊM SẢN PHẨM</span>
            <span *ngIf="loading">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              ĐANG TẢI...
            </span>
          </div>
        </div>
      </button>

      <!-- End message -->
      <div *ngIf="!hasMore && products.length > 0" class="text-center p-4">
        <p class="text-muted">Đã hiển thị tất cả sản phẩm</p>
      </div>
    </div>
  </main>

</div>
